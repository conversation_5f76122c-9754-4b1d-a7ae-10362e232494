// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using ReviewYourTipster.Infrastructure.Database;

#nullable disable

namespace ReviewYourTipster.Infrastructure.Database.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250525053807_BetOfTheDay")]
    partial class BetOfTheDay
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("public")
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("ReviewYourTipster.Domain.DailyBet.DailyBet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("BetUrl")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("bet_url");

                    b.Property<string>("FirstCompetitor")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("first_competitor");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("image_url");

                    b.Property<bool>("IsBetOfTheDay")
                        .HasColumnType("boolean")
                        .HasColumnName("is_bet_of_the_day");

                    b.Property<int>("LikeCount")
                        .HasColumnType("integer")
                        .HasColumnName("like_count");

                    b.Property<DateTime>("MatchDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("match_date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("SecondCompetitor")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("second_competitor");

                    b.HasKey("Id")
                        .HasName("pk_daily_bets");

                    b.ToTable("daily_bets", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.Like.Like", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ObjectId")
                        .HasColumnType("uuid")
                        .HasColumnName("object_id");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_likes");

                    b.ToTable("likes", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.Profile.Profile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AvatarUrl")
                        .HasColumnType("text")
                        .HasColumnName("avatar_url");

                    b.Property<string>("Bio")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("bio");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("contact_email");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("contact_phone");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean")
                        .HasColumnName("is_verified");

                    b.Property<int>("LikeCount")
                        .HasColumnType("integer")
                        .HasColumnName("like_count");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid?>("OwnerId")
                        .HasColumnType("uuid")
                        .HasColumnName("owner_id");

                    b.Property<int>("ProfitPoints")
                        .HasColumnType("integer")
                        .HasColumnName("profit_points");

                    b.Property<int>("Rating")
                        .HasColumnType("integer")
                        .HasColumnName("rating");

                    b.Property<int>("ReviewsCount")
                        .HasColumnType("integer")
                        .HasColumnName("reviews_count");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_profiles");

                    b.ToTable("profiles", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.ProfileReview.ProfileReview", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("comment");

                    b.Property<bool>("IsContested")
                        .HasColumnType("boolean")
                        .HasColumnName("is_contested");

                    b.Property<int>("LikeCount")
                        .HasColumnType("integer")
                        .HasColumnName("like_count");

                    b.Property<DateTime>("ParticipationEndDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("participation_end_date");

                    b.Property<DateTime>("ParticipationStartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("participation_start_date");

                    b.Property<Guid>("ProfileId")
                        .HasColumnType("uuid")
                        .HasColumnName("profile_id");

                    b.Property<int>("ProfileType")
                        .HasColumnType("integer")
                        .HasColumnName("profile_type");

                    b.Property<int>("Rating")
                        .HasColumnType("integer")
                        .HasColumnName("rating");

                    b.Property<int>("UseType")
                        .HasColumnType("integer")
                        .HasColumnName("use_type");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_profile_reviews");

                    b.ToTable("profile_reviews", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.RefreshToken.RefreshToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expiration_date");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("token");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_refresh_tokens");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_refresh_tokens_user_id");

                    b.ToTable("refresh_tokens", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.ReviewComment.ReviewComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<int>("LikeCount")
                        .HasColumnType("integer")
                        .HasColumnName("like_count");

                    b.Property<Guid?>("ParentCommentId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_comment_id");

                    b.Property<Guid>("ProfileReviewId")
                        .HasColumnType("uuid")
                        .HasColumnName("profile_review_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_review_comments");

                    b.ToTable("review_comments", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.Tipster.Tipster", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Email")
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<string>("Instagram")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("instagram");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Phone")
                        .HasColumnType("text")
                        .HasColumnName("phone");

                    b.Property<string>("Telegram")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("telegram");

                    b.HasKey("Id")
                        .HasName("pk_tipsters");

                    b.ToTable("tipsters", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.TipsterRequests.TipsterRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.PrimitiveCollection<List<string>>("Descriptions")
                        .HasColumnType("text[]")
                        .HasColumnName("descriptions");

                    b.Property<string>("Email")
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<string>("Instagram")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("instagram");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<int>("Origin")
                        .HasColumnType("integer")
                        .HasColumnName("origin");

                    b.Property<string>("Phone")
                        .HasColumnType("text")
                        .HasColumnName("phone");

                    b.Property<string>("Recommendation")
                        .HasColumnType("text")
                        .HasColumnName("recommendation");

                    b.Property<string>("Telegram")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("telegram");

                    b.HasKey("Id")
                        .HasName("pk_tipster_requests");

                    b.ToTable("tipster_requests", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.Users.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AvatarUrl")
                        .HasColumnType("text")
                        .HasColumnName("avatar_url");

                    b.Property<DateTime?>("BirthDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("birth_date");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("email");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean")
                        .HasColumnName("is_verified");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text")
                        .HasColumnName("password_hash");

                    b.Property<string>("Phone")
                        .HasColumnType("text")
                        .HasColumnName("phone");

                    b.Property<int>("Role")
                        .HasColumnType("integer")
                        .HasColumnName("role");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.ToTable("users", "public");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.RefreshToken.RefreshToken", b =>
                {
                    b.HasOne("ReviewYourTipster.Domain.Users.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_refresh_tokens_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ReviewYourTipster.Domain.Users.User", b =>
                {
                    b.Navigation("RefreshTokens");
                });
#pragma warning restore 612, 618
        }
    }
}
